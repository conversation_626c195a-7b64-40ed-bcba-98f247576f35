/**
* setup是位于beforeCreate和created之间的一个新的生命周期钩子，在setup中我们可以使用props、data、methods、computed、watch等，但是不能使用this
*/
<script setup lang="ts">
import LiveChat from '@/views/LiveChat.vue';
import ZeChat from '@/views/ZeChat.vue';
import { onMounted, ref, inject, onBeforeUnmount } from 'vue';
import { useI18n } from 'vue-i18n';
import { dataLayerToParent, getQueryString } from './util/util';
import EventBus from './util/eventBus';
import { initSocket } from '@/plugins/socket';

const $bus = inject<EventBus>('eventBus')
const appId = getQueryString('appId')
const liveChatStatus = ref(appId !== '2' ? false : true) // liveChat的html是否显示
const componentStatus = ref(appId !== '2' ? false : true) // 组件的状态
const originUrl = ref('') // iframe的url
const productInfo = ref(null) // 产品信息

// 语言设置
setLanguage()
onMounted(() => {
  // iframe 通信
  window.addEventListener('message', handleShowLiveChat)
  $bus.on('changeChatStatus', changeChatStatus)
  const storageStatus = localStorage.getItem('liveChatStatus')
  if (storageStatus === '1') {
    // 使用postMessage向父窗口通讯
    window.parent.postMessage({type: 'open_chat', data: 1}, '*')
    componentStatus.value = true
    liveChatStatus.value = true
  }
  // document.addEventListener('mouseleave', () => {
  //     console.log('livechat mouseleave');
  // });
  // document.addEventListener('mouseenter', () => {
  //     console.log('livechat mouseenter');
  // });
  // document.addEventListener('visibilitychange', (e) => {
  //   console.log('livechat visibilitychange:', document.hidden, new Date().getTime());
  // })
});
const handleShowLiveChat = (event: MessageEvent) => {
  if (event.data?.type === 'open_chat') {
    // 当重新打开聊天时，重置WebSocket连接状态
    const ws = initSocket()
    ws.resetConnectionState()

    liveChatStatus.value = true
    componentStatus.value = true
    localStorage.setItem('liveChatStatus', '1')
    originUrl.value = event.data.origin
    window.parent.postMessage({type: 'open_chat', data: 1}, '*')
    window.parent.postMessage({type: 'unread_message', data: false}, '*')
  } else if (event.data === 'hide_chat') {
    liveChatStatus.value = false
    localStorage.setItem('liveChatStatus', '0')
  }
  if (event.data.type === 'prod_card') {
    productInfo.value = event.data.data
  }
}
onBeforeUnmount(() => {
  window.removeEventListener('message', handleShowLiveChat)
})
const changeChatStatus = (status: string) => {
  if (appId === '2') return localStorage.setItem('show_end_btn', 'false')
  window.parent.postMessage('hide_chat', '*')
  localStorage.setItem('liveChatStatus', '0')
  switch (status) {
    case 'minimize':
      setTimeout(() => {
        liveChatStatus.value = false
      }, 300)
      dataLayerToParent({
        eventLabel: "minimize",
      })
      break;
  
    default:
      setTimeout(() => {
        componentStatus.value = false
        liveChatStatus.value = false
      }, 300)
      dataLayerToParent({
        eventLabel: "close",
      })
      break;
  }
}
function setLanguage() {
  const { locale } = useI18n()
  const site = getQueryString('webSite') || 'en'
  locale.value = site
}
</script>

<template>
  <LiveChat @changeChatStatus="changeChatStatus" :liveChatStatus="liveChatStatus" v-if="componentStatus" :originUrl="originUrl" :productInfo="productInfo"></LiveChat>
</template>

<style lang="scss">

</style>
