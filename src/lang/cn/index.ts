export default {
  popupTitle: "在线沟通",
  placeholder: "输入文本...",
  fileType: "本地文件/图片",
  chatNow: "进入对话",
  endChat: "结束聊天？",
  closeTips: "是否确实要关闭聊天？",
  closeBtn: "关闭",
  feedbackTitle: "请您为本次服务打分",
  good: "很好",
  bad: "很差",
  rateTip: "您已经打分",
  hasVoted: "谢谢您的评价！",
  commentTitle: "您有任何建议或反馈吗？",
  ratePlaceholder: "输入您的评论...",
  submit: "提交",
  read: "已读",
  unread: "未读",
  emailTranscript: "发送聊天记录至邮箱",
  mute: "关闭声音",
  unmute: "打开声音",
  emailTip: "我们会将聊天记录发送至您填写的邮箱地址",
  sendBtn: "发送邮件",
  sendStatus: "已发送",
  sendInfoStart: "此聊天记录已通过电子邮件发送至 ",
  sendInfoEnd: "。 如果您在几分钟内没有收到电子邮件，请在垃圾邮件中查找文件。",
  backChat: "返回对话",
  vaildInfo: "抱歉，您的邮箱地址有误，麻烦提供一个正确的。",
  message: "条新消息",
  messages: "条新消息",
  downloadApp: "接入飞速（FS）APP",
  more3Files: "最多上传3份文件",
  fileSize5M: "文件大小≤5M",
  fileSize20M: "文件大小≤20M",
  invalidFileType: "文件类型不支持",
  robot: "飞速（FS）虚拟助手",
  advisor: "飞速（FS）客服",
  formLabel: {
    name: "姓名",
    email: "邮箱地址（选填）",
    tel: "联系电话",
    help: "需要什么帮助?",
    Optional: "选填",
  },
  formError: {
    name: "请输入您的名字。",
    email: {
      error1: "请输入您的邮箱地址。",
      error2: "请输入有效的邮箱地址。",
    },
    tel: {
      error1: "请输入您的电话号码。",
      error2: "请输入有效的电话号码。",
    },
    help: "这是必填栏。",
  },
  typing: "正在输入中...",
  newFeedback: {
    title: "对话评分",
    comment: "评论",
    update: "更新",
  },
  userForm: {
    name: "姓名",
    email: "邮箱",
    phone: "电话号码",
    selectAgent: "选择代理人",
    selectOption: ["产品支持", "技术支持"],
    saved: "已保存",
  },
  placeholder2: "输入文本...",
  viewPastConversations: "查看历史对话",
  conversationHistory: "对话历史",
  videoTip: {
    title: "观看视频",
    text: "是否在新窗口观看视频？",
    btn: "立即开始",
  },
};
